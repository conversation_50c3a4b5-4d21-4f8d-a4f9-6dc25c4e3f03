-- IoT Device Data Pipeline: Kafka to Iceberg
-- This pipeline reads IoT device sensor data from Kafka and writes it to Iceberg

CREATE TABLE t_k_devices
  (
     id           STRING,
     value        DOUBLE
  ) WITH (
    'connector' = 'kafka',
    'topic' = 'devices',
    'properties.bootstrap.servers' = 'broker:29092',
    'scan.startup.mode' = 'earliest-offset',
    'format' = 'json'
  );


SET 'execution.checkpointing.interval' = '60sec';
SET 'pipeline.operator-chaining.enabled' = 'false';

-- Create Iceberg table for device data
-- Store all device readings with additional computed fields
CREATE TABLE t_i_devices
  WITH (
  'connector' = 'iceberg',
  'catalog-type'='hive',
  'catalog-name'='dev',
  'warehouse' = 's3a://warehouse',
  'hive-conf-dir' = './conf')
  AS
  SELECT
    id,
    value,
    CASE
      WHEN value > 50.0 THEN 'HIGH'
      WHEN value > 20.0 THEN 'MEDIUM'
      ELSE 'LOW'
    END as value_category,
    PROCTIME() as processing_time
  FROM t_k_devices;