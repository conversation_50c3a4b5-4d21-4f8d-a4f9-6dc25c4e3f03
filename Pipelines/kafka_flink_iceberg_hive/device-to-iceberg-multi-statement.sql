-- Multi-statement IoT Device Data Pipeline
-- This demonstrates multiple operations on device data

-- First, insert high-value device readings
INSERT INTO t_i_devices
SELECT
  id,
  value,
  'HIGH' as value_category,
  PROCTIME() as processing_time
FROM t_k_devices
WHERE value > 50.0;

-- Then, insert aggregated hourly statistics
INSERT INTO t_i_devices
SELECT
  CONCAT('hourly_avg_', SUBSTRING(id, 8)) as id,
  AVG(value) as value,
  'AGGREGATED' as value_category,
  PROCTIME() as processing_time
FROM t_k_devices
WHERE value > 0
GROUP BY
  SUBSTRING(id, 8),
  TUMBLE(PROCTIME(), INTERVAL '1' HOUR);