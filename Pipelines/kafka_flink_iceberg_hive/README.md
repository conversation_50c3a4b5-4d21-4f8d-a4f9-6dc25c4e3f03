# IoT Device Data Pipeline: Kafka to Iceberg with Apache Flink

This project demonstrates streaming IoT device sensor data from Kafka to Iceberg using Apache Flink SQL.

## Architecture

```
IoT Devices → Kafka Topic → Apache Flink → Iceberg Tables → MinIO Storage
```

## Data Format

### Kafka Topic Data
The pipeline expects IoT device data in the following format on the `devices` Kafka topic:

**Topic**: `devices`
**Key**: Device identifier (e.g., `device-71`)
**Value**: JSON with device readings

```json
{"id":"device-71","value":62.91}
{"id":"device-72","value":16.02}
{"id":"device-91","value":52.48}
{"id":"device-66","value":19.01}
{"id":"device-95","value":66.01}
```

### Schema
- `id` (STRING): Unique device identifier
- `value` (DOUBLE): Sensor reading value

## Quick Start

### 1. Bring up the stack
```bash
docker compose up
```

### 2. Create tables (DDL)
```bash
docker compose exec -it jobmanager bash -c "./bin/sql-client.sh -f /data/kafka_iceberg_tables_DDL.sql"
```

### 3. Start data processing (DML)
```bash
docker compose exec -it jobmanager bash -c "./bin/sql-client.sh -f /data/kafka_iceberg_DML.sql"
```

### 4. Check for data in MinIO
```bash
docker compose exec mc bash -c "mc ls -r minio/warehouse/"
```

You should see Iceberg table files under `default_database.db/t_i_devices/`

## SQL Files Structure

### DDL File (`kafka_iceberg_tables_DDL.sql`)
**Data Definition Language** - Creates table structures:
- Kafka source table (`t_k_devices`) - reads from `devices` topic
- Iceberg sink table (`t_i_devices`) - stores processed data

### DML File (`kafka_iceberg_DML.sql`)
**Data Manipulation Language** - Processes and moves data:
- Sets Flink execution parameters
- Inserts processed data from Kafka to Iceberg
- Adds value categorization (HIGH/MEDIUM/LOW)
- Adds processing timestamps

## Data Processing Features

### Value Categorization
Device readings are automatically categorized:
- **HIGH**: value > 50.0
- **MEDIUM**: 20.0 < value ≤ 50.0  
- **LOW**: value ≤ 20.0

### Timestamps
- `processing_time`: When the record was processed by Flink

## Services

| Service | Port | Description |
|---------|------|-------------|
| Flink JobManager | 8081 | Flink Web UI |
| Kafka | 9092 | Kafka broker |
| MinIO Console | 9001 | Object storage UI |
| MinIO API | 9000 | Object storage API |
| Hive Metastore | 9083 | Metadata service |
| Trino | 8080 | Query engine |
| Superset | 8088 | Data visualization |

## Examining the Data

### Using PyIceberg
```bash
docker compose exec pyiceberg bash
pyiceberg list
pyiceberg describe default_database.t_i_devices
```

### Using Trino
Connect to Trino at http://localhost:8080 and query:
```sql
SELECT * FROM iceberg.default_database.t_i_devices LIMIT 10;
```

### Using DuckDB (from Flink container)
```bash
docker exec -it jobmanager bash -c "duckdb"
```

```sql
INSTALL httpfs;
INSTALL iceberg;
LOAD httpfs;
LOAD iceberg;

CREATE SECRET secret1 (
    TYPE S3,
    KEY_ID 'admin',
    SECRET 'password',
    REGION 'us-east-1',
    ENDPOINT 'minio:9000',
    URL_STYLE 'path',
    USE_SSL 'false'
);

-- Query latest data
SELECT count(*) as total_readings,
       avg(value) as avg_value,
       max(value) as max_value,
       min(value) as min_value
FROM iceberg_scan('s3://warehouse/default_database.db/t_i_devices/metadata/[LATEST_MANIFEST]');
```

## Configuration

### Kafka Topic
Make sure your Kafka producer sends data to the `devices` topic with the expected JSON format.

### Flink Settings
- Checkpointing: Every 60 seconds
- Operator chaining: Disabled for better visibility in Flink UI

### Iceberg Settings
- Catalog: Hive Metastore
- Storage: MinIO S3-compatible storage
- Format: Parquet (default)

## Troubleshooting

### No data appearing?
1. Check if Kafka topic `devices` has data
2. Verify Flink job is running in the Web UI (http://localhost:8081)
3. Check Flink logs for errors

### Connection issues?
1. Ensure all services are up: `docker compose ps`
2. Check service logs: `docker compose logs [service-name]`

## Development

To modify the pipeline:
1. Edit the SQL files
2. Restart the Flink job
3. Monitor progress in Flink Web UI

The pipeline is designed to be easily extensible for additional IoT data processing needs.
